from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QLineEdit, QFileDialog, QMessageBox, QComboBox

class PluginTab(QWidget):
    tab_name = "AI内容生成"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # API参数
        hl1 = QHBoxLayout()
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("填写deepseek/openai API Key")
        hl1.addWidget(QLabel("API Key:"))
        hl1.addWidget(self.api_key_input)
        layout.addLayout(hl1)

        # 内容类型选择
        hl2 = QHBoxLayout()
        self.type_box = QComboBox()
        self.type_box.addItems(["邮件标题", "正文内容", "批量内容模板"])
        hl2.addWidget(QLabel("生成类型:"))
        hl2.addWidget(self.type_box)
        layout.addLayout(hl2)

        # 关键词/主题
        hl3 = QHBoxLayout()
        self.prompt_input = QLineEdit()
        self.prompt_input.setPlaceholderText("如“验证码通知”、“优惠活动”")
        hl3.addWidget(QLabel("关键词/主题:"))
        hl3.addWidget(self.prompt_input)
        layout.addLayout(hl3)

        # 生成与批量按钮
        btn_layout = QHBoxLayout()
        self.gen_btn = QPushButton("生成")
        self.gen_btn.clicked.connect(self.generate)
        btn_layout.addWidget(self.gen_btn)
        self.batch_btn = QPushButton("批量生成")
        self.batch_btn.clicked.connect(self.batch_generate)
        btn_layout.addWidget(self.batch_btn)
        layout.addLayout(btn_layout)

        # 结果区
        self.result_box = QTextEdit()
        self.result_box.setPlaceholderText("生成结果将在这里显示...")
        layout.addWidget(self.result_box)

        # 导出内容
        export_layout = QHBoxLayout()
        self.export_btn = QPushButton("导出内容")
        self.export_btn.clicked.connect(self.export_content)
        export_layout.addWidget(self.export_btn)
        layout.addLayout(export_layout)

        self.setLayout(layout)

    def generate(self):
        # 占位：这里可接入deepseek/openai接口
        content_type = self.type_box.currentText()
        prompt = self.prompt_input.text().strip()
        if not prompt:
            QMessageBox.warning(self, "缺少关键词", "请填写关键词/主题")
            return
        if content_type == "邮件标题":
            result = f"[AI] {prompt} - 您有新的重要通知"
        elif content_type == "正文内容":
            result = f"[AI] 尊敬的用户，您的{prompt}，如有疑问请联系官方。"
        else:
            # 批量模板，简单示例
            result = "\n".join([f"[AI模板] {prompt} {i}" for i in range(1, 6)])
        self.result_box.setText(result)

    def batch_generate(self):
        # 占位：这里可接入API批量生成
        prompt = self.prompt_input.text().strip()
        if not prompt:
            QMessageBox.warning(self, "缺少关键词", "请填写关键词/主题")
            return
        results = []
        for i in range(10):
            results.append(f"[AI批量] {prompt}（序号{i+1}）— 本邮件仅供演示")
        self.result_box.setText("\n".join(results))

    def export_content(self):
        content = self.result_box.toPlainText().strip()
        if not content:
            QMessageBox.warning(self, "导出失败", "没有可导出的内容！")
            return
        fname, _ = QFileDialog.getSaveFileName(self, "导出内容", "ai_content.txt", "Text Files (*.txt)")
        if fname:
            with open(fname, "w", encoding="utf-8") as f:
                f.write(content)
            QMessageBox.information(self, "导出成功", "内容已导出到:\n" + fname)
