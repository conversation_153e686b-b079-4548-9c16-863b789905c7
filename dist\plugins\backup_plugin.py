from PyQt5.QtWidgets import QWidget, QVBoxLayout, QPushButton, QTextEdit, QListWidget, QFileDialog, QMessageBox
import os, shutil, time

DATA_DIR = "data"
BACKUP_DIR = "backup"
os.makedirs(BACKUP_DIR, exist_ok=True)

class PluginTab(QWidget):
    tab_name = "快照/回滚"
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_backups()

    def init_ui(self):
        layout = QVBoxLayout()
        self.backup_btn = QPushButton("手动快照")
        self.backup_btn.clicked.connect(self.backup_data)
        layout.addWidget(self.backup_btn)
        self.restore_btn = QPushButton("还原选中快照")
        self.restore_btn.clicked.connect(self.restore_data)
        layout.addWidget(self.restore_btn)
        self.list = QListWidget()
        layout.addWidget(self.list)
        self.setLayout(layout)

    def backup_data(self):
        ts = time.strftime("%Y%m%d_%H%M%S")
        snap_dir = os.path.join(BACKUP_DIR, f"snap_{ts}")
        os.makedirs(snap_dir, exist_ok=True)
        for fname in os.listdir(DATA_DIR):
            shutil.copy(os.path.join(DATA_DIR, fname), snap_dir)
        self.load_backups()

    def load_backups(self):
        self.list.clear()
        snaps = sorted([f for f in os.listdir(BACKUP_DIR) if f.startswith("snap_")])
        self.list.addItems(snaps[::-1])

    def restore_data(self):
        snap = self.list.currentItem()
        if not snap:
            QMessageBox.warning(self, "未选快照", "请先选择一个快照")
            return
        snap_dir = os.path.join(BACKUP_DIR, snap.text())
        for fname in os.listdir(snap_dir):
            shutil.copy(os.path.join(snap_dir, fname), os.path.join(DATA_DIR, fname))
        QMessageBox.information(self, "还原成功", "主数据已还原到所选快照")
