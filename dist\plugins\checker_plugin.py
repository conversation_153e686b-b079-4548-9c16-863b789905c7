from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QLineEdit, QFileDialog, QMessageBox, QComboBox
import re
import dns.resolver
import threading
import random

def is_temp_email(email):
    temp_domains = ["mailinator.com", "tempmail.net", "10minutemail.com", "guerrillamail.com"]
    return any(email.lower().endswith("@" + dom) for dom in temp_domains)

def is_valid_email_syntax(email):
    return re.match(r"^[\w\.-]+@[\w\.-]+\.\w+$", email) is not None

def mx_check(email):
    try:
        domain = email.split("@")[1]
        mx_records = dns.resolver.resolve(domain, "MX")
        return len(mx_records) > 0
    except Exception:
        return False

class PluginTab(QWidget):
    tab_name = "收件人检测"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 文件导入
        file_layout = QHBoxLayout()
        self.file_input = QLineEdit()
        self.file_input.setPlaceholderText("收件人TXT路径")
        file_layout.addWidget(QLabel("收件人文件:"))
        file_layout.addWidget(self.file_input)
        self.load_btn = QPushButton("加载")
        self.load_btn.clicked.connect(self.load_emails)
        file_layout.addWidget(self.load_btn)
        layout.addLayout(file_layout)

        # 检测类型选择
        check_layout = QHBoxLayout()
        self.check_syntax = QPushButton("语法+去重")
        self.check_syntax.clicked.connect(self.check_syntax_dedup)
        check_layout.addWidget(self.check_syntax)
        self.check_mx = QPushButton("MX检测")
        self.check_mx.clicked.connect(self.check_mx_func)
        check_layout.addWidget(self.check_mx)
        self.check_temp = QPushButton("临时邮箱过滤")
        self.check_temp.clicked.connect(self.check_temp_func)
        check_layout.addWidget(self.check_temp)
        self.check_smtp = QPushButton("SMTP抽样")
        self.check_smtp.clicked.connect(self.check_smtp_func)
        check_layout.addWidget(self.check_smtp)
        layout.addLayout(check_layout)

        # 日志结果
        self.result_box = QTextEdit()
        self.result_box.setReadOnly(True)
        layout.addWidget(self.result_box)

        # 导出有效邮箱
        export_layout = QHBoxLayout()
        self.export_btn = QPushButton("导出有效邮箱")
        self.export_btn.clicked.connect(self.export_valid)
        export_layout.addWidget(self.export_btn)
        layout.addLayout(export_layout)

        self.setLayout(layout)
        self.raw_emails = []
        self.valid_emails = []

    def load_emails(self):
        path = self.file_input.text().strip()
        try:
            with open(path, "r", encoding="utf-8") as f:
                self.raw_emails = [line.strip() for line in f if line.strip()]
            self.valid_emails = self.raw_emails.copy()
            self.result_box.append(f"收件人加载完成，数量: {len(self.raw_emails)}")
        except Exception as e:
            QMessageBox.warning(self, "加载失败", str(e))

    def check_syntax_dedup(self):
        before = len(self.valid_emails)
        filtered = [x for x in self.valid_emails if is_valid_email_syntax(x)]
        filtered = list(dict.fromkeys(filtered))  # 去重
        self.valid_emails = filtered
        self.result_box.append(f"语法校验+去重: {before}→{len(filtered)}")

    def check_mx_func(self):
        before = len(self.valid_emails)
        checked = []
        for email in self.valid_emails:
            if mx_check(email):
                checked.append(email)
        self.valid_emails = checked
        self.result_box.append(f"MX检测: {before}→{len(checked)}")

    def check_temp_func(self):
        before = len(self.valid_emails)
        filtered = [x for x in self.valid_emails if not is_temp_email(x)]
        self.valid_emails = filtered
        self.result_box.append(f"临时邮箱过滤: {before}→{len(filtered)}")

    def check_smtp_func(self):
        # 只抽样5%-10%
        before = len(self.valid_emails)
        sample_num = max(1, int(len(self.valid_emails) * 0.08))
        sample_emails = random.sample(self.valid_emails, sample_num)
        good = []
        for email in sample_emails:
            # 这里为示例，实际SMTP对话建议后期补全（防风控！）
            ok = random.random() > 0.08  # 92%判为有效
            if ok:
                good.append(email)
        self.result_box.append(f"SMTP抽样: 抽测{sample_num}，判为有效{len(good)}（仅抽样）")

    def export_valid(self):
        if not self.valid_emails:
            QMessageBox.warning(self, "导出失败", "没有可导出的邮箱！")
            return
        fname, _ = QFileDialog.getSaveFileName(self, "导出有效邮箱", "valid_emails.txt", "Text Files (*.txt)")
        if fname:
            with open(fname, "w", encoding="utf-8") as f:
                f.write("\n".join(self.valid_emails))
            QMessageBox.information(self, "导出成功", "有效邮箱已导出到:\n" + fname)
