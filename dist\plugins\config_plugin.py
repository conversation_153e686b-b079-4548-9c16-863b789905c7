from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QLineEdit, QFileDialog, QMessageBox, QGroupBox
import json
import os

CONFIG_PATH = "config/settings.json"

def load_config():
    if not os.path.exists(CONFIG_PATH):
        return {}
    with open(CONFIG_PATH, "r", encoding="utf-8") as f:
        return json.load(f)

def save_config(cfg):
    os.makedirs(os.path.dirname(CONFIG_PATH), exist_ok=True)
    with open(CONFIG_PATH, "w", encoding="utf-8") as f:
        json.dump(cfg, f, ensure_ascii=False, indent=2)

class PluginTab(QWidget):
    tab_name = "参数&安全中心"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        self.cfg = load_config()
        layout = QVBoxLayout()

        # 基础参数设置
        box1 = QGroupBox("批量参数")
        l1 = QHBoxLayout()
        self.thread_input = QLineEdit(str(self.cfg.get("default_threads", 30)))
        self.thread_input.setPlaceholderText("如30")
        l1.addWidget(QLabel("默认线程数:"))
        l1.addWidget(self.thread_input)
        self.delay_input = QLineEdit(str(self.cfg.get("default_delay", 3)))
        self.delay_input.setPlaceholderText("如3")
        l1.addWidget(QLabel("默认延迟(秒):"))
        l1.addWidget(self.delay_input)
        box1.setLayout(l1)
        layout.addWidget(box1)

        # 代理池、API密钥
        box2 = QGroupBox("全局API/代理池")
        l2 = QHBoxLayout()
        self.proxy_input = QLineEdit(self.cfg.get("proxy_api", ""))
        self.proxy_input.setPlaceholderText("代理池API")
        l2.addWidget(QLabel("代理池API:"))
        l2.addWidget(self.proxy_input)
        self.deepseek_input = QLineEdit(self.cfg.get("deepseek_key", ""))
        self.deepseek_input.setPlaceholderText("deepseek/openai API Key")
        l2.addWidget(QLabel("AI API Key:"))
        l2.addWidget(self.deepseek_input)
        box2.setLayout(l2)
        layout.addWidget(box2)

        # 激活码、硬件绑定
        box3 = QGroupBox("授权安全")
        l3 = QHBoxLayout()
        self.sn_input = QLineEdit(self.cfg.get("serial_no", ""))
        self.sn_input.setPlaceholderText("请输入激活码")
        l3.addWidget(QLabel("激活码:"))
        l3.addWidget(self.sn_input)
        self.hwid_input = QLineEdit(self.cfg.get("hwid", ""))
        self.hwid_input.setPlaceholderText("硬盘指纹(自动/人工填)")
        l3.addWidget(QLabel("硬件ID:"))
        l3.addWidget(self.hwid_input)
        box3.setLayout(l3)
        layout.addWidget(box3)

        # 保存按钮
        btn_layout = QHBoxLayout()
        self.save_btn = QPushButton("保存配置")
        self.save_btn.clicked.connect(self.save_all)
        btn_layout.addWidget(self.save_btn)
        layout.addLayout(btn_layout)

        # 显示配置
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)

        self.setLayout(layout)
        self.show_cfg()

    def save_all(self):
        self.cfg["default_threads"] = int(self.thread_input.text() or 30)
        self.cfg["default_delay"] = int(self.delay_input.text() or 3)
        self.cfg["proxy_api"] = self.proxy_input.text().strip()
        self.cfg["deepseek_key"] = self.deepseek_input.text().strip()
        self.cfg["serial_no"] = self.sn_input.text().strip()
        self.cfg["hwid"] = self.hwid_input.text().strip()
        save_config(self.cfg)
        self.log_box.append("配置已保存！")
        self.show_cfg()

    def show_cfg(self):
        self.log_box.setText(json.dumps(self.cfg, ensure_ascii=False, indent=2))
