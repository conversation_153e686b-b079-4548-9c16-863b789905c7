from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QFileDialog, QMessageBox, QComboBox
import openpyxl
import os

class PluginTab(QWidget):
    tab_name = "EXCEL导入导出"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 类型选择
        type_layout = QHBoxLayout()
        self.type_box = QComboBox()
        self.type_box.addItems([
            "账号池", "收件池", "日志", "内容模板"
        ])
        type_layout.addWidget(QLabel("类型:"))
        type_layout.addWidget(self.type_box)
        layout.addLayout(type_layout)

        # 按钮区
        btn_layout = QHBoxLayout()
        self.import_btn = QPushButton("导入EXCEL")
        self.import_btn.clicked.connect(self.import_excel)
        btn_layout.addWidget(self.import_btn)
        self.export_btn = QPushButton("导出为EXCEL")
        self.export_btn.clicked.connect(self.export_excel)
        btn_layout.addWidget(self.export_btn)
        layout.addLayout(btn_layout)

        # 日志
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)

        self.setLayout(layout)

    def import_excel(self):
        typ = self.type_box.currentText()
        fname, _ = QFileDialog.getOpenFileName(self, "导入EXCEL", "", "Excel Files (*.xlsx *.xls)")
        if not fname:
            return
        try:
            wb = openpyxl.load_workbook(fname)
            ws = wb.active
            data = []
            for row in ws.iter_rows(values_only=True):
                if any(row):
                    data.append(list(row))
            # 导入数据写入本地txt/json/池文件，具体分支可扩展
            if typ == "账号池":
                with open("data/accounts.txt", "w", encoding="utf-8") as f:
                    for row in data:
                        if len(row) >= 3:
                            f.write("----".join(str(x) for x in row[:3]) + "\n")
            elif typ == "收件池":
                with open("data/recipients.txt", "w", encoding="utf-8") as f:
                    for row in data:
                        if row and row[0]:
                            f.write(str(row[0]) + "\n")
            elif typ == "日志":
                with open("data/excel_import_log.txt", "w", encoding="utf-8") as f:
                    for row in data:
                        f.write("\t".join(str(x) for x in row) + "\n")
            elif typ == "内容模板":
                import json
                tpl = [{"title": str(row[0]), "body": str(row[1])} for row in data if len(row) >= 2]
                with open("data/templates.json", "w", encoding="utf-8") as f:
                    json.dump(tpl, f, ensure_ascii=False, indent=2)
            self.log_box.append(f"导入成功，类型:{typ}，数据行数:{len(data)}")
        except Exception as e:
            self.log_box.append(f"导入失败: {e}")

    def export_excel(self):
        typ = self.type_box.currentText()
        fname, _ = QFileDialog.getSaveFileName(self, "导出为EXCEL", f"{typ}.xlsx", "Excel Files (*.xlsx)")
        if not fname:
            return
        try:
            wb = openpyxl.Workbook()
            ws = wb.active
            data = []
            if typ == "账号池":
                if os.path.exists("data/accounts.txt"):
                    with open("data/accounts.txt", "r", encoding="utf-8") as f:
                        for line in f:
                            row = line.strip().split("----")
                            ws.append(row)
                ws.title = "账号池"
            elif typ == "收件池":
                if os.path.exists("data/recipients.txt"):
                    with open("data/recipients.txt", "r", encoding="utf-8") as f:
                        for line in f:
                            ws.append([line.strip()])
                ws.title = "收件池"
            elif typ == "日志":
                if os.path.exists("data/excel_import_log.txt"):
                    with open("data/excel_import_log.txt", "r", encoding="utf-8") as f:
                        for line in f:
                            ws.append(line.strip().split("\t"))
                ws.title = "日志"
            elif typ == "内容模板":
                import json
                if os.path.exists("data/templates.json"):
                    with open("data/templates.json", "r", encoding="utf-8") as f:
                        tpl = json.load(f)
                        for item in tpl:
                            ws.append([item["title"], item["body"]])
                ws.title = "模板"
            wb.save(fname)
            self.log_box.append(f"导出成功，类型:{typ}，路径:{fname}")
        except Exception as e:
            self.log_box.append(f"导出失败: {e}")
