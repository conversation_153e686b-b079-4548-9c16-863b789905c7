from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QLineEdit, QFileDialog, QMessageBox, QComboBox
import os
import datetime

LOG_DIR = "data/logs"
os.makedirs(LOG_DIR, exist_ok=True)

def get_log_files():
    return [f for f in os.listdir(LOG_DIR) if f.endswith(".log")]

def read_log(path):
    with open(path, "r", encoding="utf-8") as f:
        return f.read()

class PluginTab(QWidget):
    tab_name = "日志与报表"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 日志类型选择与刷新
        hl1 = QHBoxLayout()
        self.type_box = QComboBox()
        self.type_box.addItems(["全部日志"] + get_log_files())
        hl1.addWidget(QLabel("选择日志:"))
        hl1.addWidget(self.type_box)
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_logs)
        hl1.addWidget(self.refresh_btn)
        layout.addLayout(hl1)

        # 日志内容显示区
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)

        # 导出日志与报表
        btn_layout = QHBoxLayout()
        self.export_btn = QPushButton("导出当前日志")
        self.export_btn.clicked.connect(self.export_log)
        btn_layout.addWidget(self.export_btn)
        self.export_report_btn = QPushButton("导出统计报表")
        self.export_report_btn.clicked.connect(self.export_report)
        btn_layout.addWidget(self.export_report_btn)
        layout.addLayout(btn_layout)

        self.setLayout(layout)
        self.refresh_logs()

    def refresh_logs(self):
        selected = self.type_box.currentText()
        if selected == "全部日志":
            logs = ""
            for fname in get_log_files():
                logs += f"=== {fname} ===\n"
                logs += read_log(os.path.join(LOG_DIR, fname)) + "\n"
            self.log_box.setText(logs)
        else:
            path = os.path.join(LOG_DIR, selected)
            if os.path.exists(path):
                self.log_box.setText(read_log(path))
            else:
                self.log_box.setText("")

    def export_log(self):
        selected = self.type_box.currentText()
        content = self.log_box.toPlainText().strip()
        if not content:
            QMessageBox.warning(self, "导出失败", "无日志内容可导出！")
            return
        fname, _ = QFileDialog.getSaveFileName(self, "导出日志", selected.replace('.log','') + "_log.txt", "Text Files (*.txt)")
        if fname:
            with open(fname, "w", encoding="utf-8") as f:
                f.write(content)
            QMessageBox.information(self, "导出成功", f"日志已导出到:\n{fname}")

    def export_report(self):
        # 报表可按群发/注册/养号等分类型统计，演示导出总条数
        selected = self.type_box.currentText()
        content = self.log_box.toPlainText().strip()
        stat_lines = content.splitlines()
        total = len(stat_lines)
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        report = f"【EM sent 操作报表】\n日志类型: {selected}\n导出时间: {today}\n记录条数: {total}\n\n---\n{content[:500]}...\n"
        fname, _ = QFileDialog.getSaveFileName(self, "导出统计报表", f"report_{today}.txt", "Text Files (*.txt)")
        if fname:
            with open(fname, "w", encoding="utf-8") as f:
                f.write(report)
            QMessageBox.information(self, "导出成功", f"报表已导出到:\n{fname}")
