from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QLineEdit, QComboBox, QMessageBox
import requests
import json

NOTIFY_PATH = "config/notify_apis.json"

def load_notify():
    try:
        with open(NOTIFY_PATH, "r", encoding="utf-8") as f:
            return json.load(f)
    except:
        return []

def save_notify(data):
    with open(NOTIFY_PATH, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

class PluginTab(QWidget):
    tab_name = "短信通知"

    def __init__(self):
        super().__init__()
        self.apis = load_notify()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        # 列表
        self.api_box = QComboBox()
        self.api_box.addItems([api['name'] for api in self.apis] or ["无通知API"])
        layout.addWidget(QLabel("选择通知API:"))
        layout.addWidget(self.api_box)

        # 通知内容
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("手机号/邮箱/推送ID（批量用英文逗号分隔）")
        layout.addWidget(self.phone_input)
        self.msg_input = QTextEdit()
        self.msg_input.setPlaceholderText("通知内容")
        layout.addWidget(self.msg_input)
        self.send_btn = QPushButton("发送通知")
        self.send_btn.clicked.connect(self.send_notify)
        layout.addWidget(self.send_btn)

        # API管理
        api_mgmt = QHBoxLayout()
        self.api_name_input = QLineEdit()
        self.api_name_input.setPlaceholderText("API名称")
        api_mgmt.addWidget(self.api_name_input)
        self.api_url_input = QLineEdit()
        self.api_url_input.setPlaceholderText("URL")
        api_mgmt.addWidget(self.api_url_input)
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("API Key/Token(选填)")
        api_mgmt.addWidget(self.api_key_input)
        self.api_type_box = QComboBox()
        self.api_type_box.addItems(["短信", "微信", "TG", "邮箱", "自定义"])
        api_mgmt.addWidget(self.api_type_box)
        self.add_api_btn = QPushButton("添加API")
        self.add_api_btn.clicked.connect(self.add_api)
        api_mgmt.addWidget(self.add_api_btn)
        layout.addLayout(api_mgmt)

        # 日志
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)

        self.setLayout(layout)

    def add_api(self):
        name = self.api_name_input.text().strip()
        url = self.api_url_input.text().strip()
        typ = self.api_type_box.currentText()
        key = self.api_key_input.text().strip()
        if not name or not url:
            QMessageBox.warning(self, "缺少参数", "API名称和URL不能为空")
            return
        api = {"name": name, "url": url, "type": typ, "key": key}
        self.apis.append(api)
        save_notify(self.apis)
        self.api_box.addItem(name)
        self.log_box.append(f"已添加API：{name}")

    def send_notify(self):
        idx = self.api_box.currentIndex()
        if idx < 0 or not self.apis:
            QMessageBox.warning(self, "未配置", "未选择有效API")
            return
        api = self.apis[idx]
        tos = self.phone_input.text().strip().split(",")
        msg = self.msg_input.toPlainText().strip()
        if not tos or not msg:
            QMessageBox.warning(self, "参数缺失", "收件人和内容不能为空")
            return
        succ, fail = 0, 0
        for to in tos:
            try:
                payload = {"to": to.strip(), "msg": msg}
                headers = {"Authorization": api["key"]} if api["key"] else {}
                r = requests.post(api["url"], data=payload, headers=headers, timeout=10)
                if r.status_code == 200:
                    succ += 1
                else:
                    fail += 1
            except Exception as e:
                fail += 1
        self.log_box.append(f"已发送 {succ} 条，失败 {fail} 条")
