from PyQt5.QtWidgets import QWidget, QVBoxLayout, QPushButton, QLineEdit, QTextEdit, QLabel, QSpinBox, QHBoxLayout

class PluginTab(QWidget):
    tab_name = "代理池/IP设置"
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.addWidget(QLabel("代理池API或代理列表路径："))
        self.proxy_edit = QLineEdit()
        layout.addWidget(self.proxy_edit)
        box = QHBoxLayout()
        box.addWidget(QLabel("单IP最大使用次数:"))
        self.ipmax_spin = QSpinBox()
        self.ipmax_spin.setValue(3)
        box.addWidget(self.ipmax_spin)
        box.addWidget(QLabel("IP存活秒数:"))
        self.ipt_sec = QSpinBox()
        self.ipt_sec.setValue(60)
        box.addWidget(self.ipt_sec)
        layout.addLayout(box)
        self.save_btn = QPushButton("保存设置")
        self.save_btn.clicked.connect(self.save)
        layout.addWidget(self.save_btn)
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)
        self.setLayout(layout)

    def save(self):
        proxy_path = self.proxy_edit.text().strip()
        ipmax = self.ipmax_spin.value()
        ipsec = self.ipt_sec.value()
        with open("config/proxy.txt", "w", encoding="utf-8") as f:
            f.write(f"{proxy_path}|{ipmax}|{ipsec}\n")
        self.log_box.append("代理/IP配置已保存。")
