from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QGroupBox, QTextEdit, QFileDialog, QLineEdit, QComboBox, QMessageBox
import threading

class PluginTab(QWidget):
    tab_name = "注册机"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 邮箱类型选择
        box1 = QGroupBox("注册参数")
        l1 = QHBoxLayout()
        self.email_type = QComboBox()
        self.email_type.addItems(["Gmail", "Outlook"])
        l1.addWidget(QLabel("邮箱类型:"))
        l1.addWidget(self.email_type)
        l1.addWidget(QLabel("注册数量:"))
        self.count_input = QLineEdit()
        self.count_input.setPlaceholderText("比如 10")
        l1.addWidget(self.count_input)
        box1.setLayout(l1)
        layout.addWidget(box1)

        # 代理池与打码API输入
        box2 = QGroupBox("辅助参数")
        l2 = QHBoxLayout()
        self.proxy_input = QLineEdit()
        self.proxy_input.setPlaceholderText("代理池API/格式文件路径")
        l2.addWidget(QLabel("代理设置:"))
        l2.addWidget(self.proxy_input)
        self.captcha_api_input = QLineEdit()
        self.captcha_api_input.setPlaceholderText("打码平台API-key")
        l2.addWidget(QLabel("打码平台API:"))
        l2.addWidget(self.captcha_api_input)
        box2.setLayout(l2)
        layout.addWidget(box2)

        # 手机号池API输入
        box3 = QGroupBox("接码平台参数")
        l3 = QHBoxLayout()
        self.sms_input = QLineEdit()
        self.sms_input.setPlaceholderText("接码平台API-key/Token")
        l3.addWidget(QLabel("手机号池API:"))
        l3.addWidget(self.sms_input)
        box3.setLayout(l3)
        layout.addWidget(box3)

        # 开始/暂停按钮
        btn_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始注册")
        self.start_btn.clicked.connect(self.start_register)
        btn_layout.addWidget(self.start_btn)
        self.stop_btn = QPushButton("暂停")
        self.stop_btn.setEnabled(False)
        btn_layout.addWidget(self.stop_btn)
        layout.addLayout(btn_layout)

        # 进度显示
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)

        # 导出账号按钮
        export_layout = QHBoxLayout()
        self.export_btn = QPushButton("导出账号池")
        self.export_btn.clicked.connect(self.export_accounts)
        export_layout.addWidget(self.export_btn)
        layout.addLayout(export_layout)

        self.setLayout(layout)

        # 注册号池
        self.account_pool = []
        self.stop_flag = False

    def start_register(self):
        email_type = self.email_type.currentText()
        count = self.count_input.text()
        proxy = self.proxy_input.text()
        captcha_api = self.captcha_api_input.text()
        sms_api = self.sms_input.text()
        if not count.isdigit() or int(count) <= 0:
            QMessageBox.warning(self, "输入错误", "注册数量必须为正整数")
            return
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.log_box.append("【开始注册】类型:{} 数量:{}...".format(email_type, count))
        self.stop_flag = False
        threading.Thread(target=self.fake_register, args=(email_type, int(count), proxy, captcha_api, sms_api)).start()

    def fake_register(self, email_type, count, proxy, captcha_api, sms_api):
        import time
        import random
        for i in range(1, count + 1):
            if self.stop_flag:
                self.log_box.append("【已暂停】剩余任务未执行")
                break
            email = "test{}@{}mail.com".format(random.randint(100000,999999), "g" if email_type=="Gmail" else "outlook")
            refresh_token = "refresh_token_" + str(random.randint(100000,999999))
            smtp_server = "smtp.gmail.com" if email_type=="Gmail" else "smtp.office365.com"
            port = "587"
            row = "{}----{}----{}----{}".format(email, refresh_token, smtp_server, port)
            self.account_pool.append(row)
            self.log_box.append("注册成功: " + row)
            time.sleep(0.5)
        self.log_box.append("【注册结束】总计: {} 条".format(len(self.account_pool)))
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

    def export_accounts(self):
        if not self.account_pool:
            QMessageBox.warning(self, "导出失败", "暂无注册账号！")
            return
        fname, _ = QFileDialog.getSaveFileName(self, "导出账号池", "accounts.txt", "Text Files (*.txt)")
        if fname:
            with open(fname, "w", encoding="utf-8") as f:
                f.write("\n".join(self.account_pool))
            QMessageBox.information(self, "导出成功", "账号池已导出到:\n" + fname)
