from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QLineEdit, QFileDialog, QMessageBox, QGroupBox, QComboBox, QTableWidget, QTableWidgetItem
import os
import json

TEMPLATE_PATH = "data/templates.json"
os.makedirs("data", exist_ok=True)

def load_templates():
    if not os.path.exists(TEMPLATE_PATH):
        return []
    with open(TEMPLATE_PATH, "r", encoding="utf-8") as f:
        return json.load(f)

def save_templates(templates):
    with open(TEMPLATE_PATH, "w", encoding="utf-8") as f:
        json.dump(templates, f, ensure_ascii=False, indent=2)

class PluginTab(QWidget):
    tab_name = "内容模板库"

    def __init__(self):
        super().__init__()
        self.templates = load_templates()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 模板列表
        self.table = QTableWidget(0, 3)
        self.table.setHorizontalHeaderLabels(["ID", "标题", "正文"])
        layout.addWidget(self.table)

        # 编辑区
        edit_layout = QHBoxLayout()
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("模板标题")
        edit_layout.addWidget(QLabel("标题:"))
        edit_layout.addWidget(self.title_input)
        self.body_input = QTextEdit()
        self.body_input.setPlaceholderText("模板正文")
        edit_layout.addWidget(QLabel("正文:"))
        edit_layout.addWidget(self.body_input)
        layout.addLayout(edit_layout)

        # 按钮区
        btn_layout = QHBoxLayout()
        self.add_btn = QPushButton("添加模板")
        self.add_btn.clicked.connect(self.add_template)
        btn_layout.addWidget(self.add_btn)
        self.del_btn = QPushButton("删除选中")
        self.del_btn.clicked.connect(self.del_template)
        btn_layout.addWidget(self.del_btn)
        self.export_btn = QPushButton("导出模板")
        self.export_btn.clicked.connect(self.export_templates)
        btn_layout.addWidget(self.export_btn)
        self.import_btn = QPushButton("导入模板")
        self.import_btn.clicked.connect(self.import_templates)
        btn_layout.addWidget(self.import_btn)
        layout.addLayout(btn_layout)

        # 日志
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)

        self.setLayout(layout)
        self.refresh_table()

    def refresh_table(self):
        self.table.setRowCount(0)
        for i, tpl in enumerate(self.templates):
            self.table.insertRow(self.table.rowCount())
            self.table.setItem(self.table.rowCount()-1, 0, QTableWidgetItem(str(i+1)))
            self.table.setItem(self.table.rowCount()-1, 1, QTableWidgetItem(tpl.get("title", "")))
            self.table.setItem(self.table.rowCount()-1, 2, QTableWidgetItem(tpl.get("body", "")))

    def add_template(self):
        title = self.title_input.text().strip()
        body = self.body_input.toPlainText().strip()
        if not title or not body:
            QMessageBox.warning(self, "缺少内容", "标题和正文都不能为空")
            return
        self.templates.append({"title": title, "body": body})
        save_templates(self.templates)
        self.refresh_table()
        self.log_box.append(f"已添加模板: {title}")

    def del_template(self):
        row = self.table.currentRow()
        if row < 0: return
        title = self.templates[row].get("title", "")
        del self.templates[row]
        save_templates(self.templates)
        self.refresh_table()
        self.log_box.append(f"已删除模板: {title}")

    def export_templates(self):
        if not self.templates:
            QMessageBox.warning(self, "导出失败", "暂无模板")
            return
        fname, _ = QFileDialog.getSaveFileName(self, "导出模板", "templates.json", "JSON Files (*.json)")
        if fname:
            with open(fname, "w", encoding="utf-8") as f:
                json.dump(self.templates, f, ensure_ascii=False, indent=2)
            QMessageBox.information(self, "导出成功", f"模板已导出到:\n{fname}")

    def import_templates(self):
        fname, _ = QFileDialog.getOpenFileName(self, "导入模板", "", "JSON Files (*.json)")
        if not fname: return
        with open(fname, "r", encoding="utf-8") as f:
            self.templates = json.load(f)
        save_templates(self.templates)
        self.refresh_table()
        self.log_box.append("模板已批量导入。")
