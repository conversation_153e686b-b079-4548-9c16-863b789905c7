from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QComboBox, QMessageBox
from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtCore import Qt

THEMES = {
    "经典明亮": {
        "window": "#FFFFFF",
        "text": "#000000",
        "button": "#E0E0E0",
        "highlight": "#357AFF",
    },
    "科技深色": {
        "window": "#181A20",
        "text": "#FFFFFF",
        "button": "#2B2B38",
        "highlight": "#FF47C1",
    },
    "赛博朋克": {
        "window": "#11131B",
        "text": "#A4FFFA",
        "button": "#161E2E",
        "highlight": "#FF4EEB",
    },
}

class PluginTab(QWidget):
    tab_name = "主题皮肤"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        hl = QHBoxLayout()
        self.theme_box = QComboBox()
        self.theme_box.addItems(list(THEMES.keys()))
        hl.addWidget(QLabel("选择主题风格:"))
        hl.addWidget(self.theme_box)
        self.apply_btn = QPushButton("应用主题")
        self.apply_btn.clicked.connect(self.apply_theme)
        hl.addWidget(self.apply_btn)
        layout.addLayout(hl)

        self.tips = QLabel("注：全局UI风格实时切换，无需重启。建议夜间用深色，白天用明亮。")
        layout.addWidget(self.tips)

        self.setLayout(layout)

    def apply_theme(self):
        theme_name = self.theme_box.currentText()
        theme = THEMES[theme_name]
        pal = self.parentWidget().palette() if self.parentWidget() else self.palette()
        pal.setColor(QPalette.Window, QColor(theme["window"]))
        pal.setColor(QPalette.WindowText, QColor(theme["text"]))
        pal.setColor(QPalette.Base, QColor(theme["window"]))
        pal.setColor(QPalette.AlternateBase, QColor(theme["button"]))
        pal.setColor(QPalette.ToolTipBase, QColor(theme["highlight"]))
        pal.setColor(QPalette.ToolTipText, QColor(theme["text"]))
        pal.setColor(QPalette.Button, QColor(theme["button"]))
        pal.setColor(QPalette.ButtonText, QColor(theme["text"]))
        pal.setColor(QPalette.Highlight, QColor(theme["highlight"]))
        pal.setColor(QPalette.HighlightedText, QColor(theme["window"]))
        # 应用到全局
        from PyQt5.QtWidgets import QApplication
        QApplication.instance().setPalette(pal)
        QMessageBox.information(self, "主题已切换", f"已应用主题：{theme_name}")
