from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QTextEdit, QCheckBox,
    QComboBox, QMessageBox, QSplitter, QLineEdit, QFileDialog
)
from PyQt5.QtCore import Qt
import json
import os

UICFG_PATH = "config/ui_settings.json"
os.makedirs("config", exist_ok=True)

def load_ui_cfg():
    if not os.path.exists(UICFG_PATH):
        return {}
    with open(UICFG_PATH, "r", encoding="utf-8") as f:
        return json.load(f)

def save_ui_cfg(cfg):
    with open(UICFG_PATH, "w", encoding="utf-8") as f:
        json.dump(cfg, f, ensure_ascii=False, indent=2)

class PluginTab(QWidget):
    tab_name = "界面自定义"

    def __init__(self):
        super().__init__()
        self.cfg = load_ui_cfg()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 分区显示隐藏
        self.show_log_chk = QCheckBox("显示日志区")
        self.show_log_chk.setChecked(self.cfg.get("show_log", True))
        layout.addWidget(self.show_log_chk)

        self.show_sidebar_chk = QCheckBox("显示侧边栏")
        self.show_sidebar_chk.setChecked(self.cfg.get("show_sidebar", True))
        layout.addWidget(self.show_sidebar_chk)

        # 窗口宽高自定义
        w_layout = QHBoxLayout()
        self.width_input = QLineEdit(str(self.cfg.get("width", 1200)))
        self.height_input = QLineEdit(str(self.cfg.get("height", 700)))
        w_layout.addWidget(QLabel("窗口宽:"))
        w_layout.addWidget(self.width_input)
        w_layout.addWidget(QLabel("窗口高:"))
        w_layout.addWidget(self.height_input)
        layout.addLayout(w_layout)

        # 快捷键自定义
        hotkey_layout = QHBoxLayout()
        self.hotkey_box = QComboBox()
        self.hotkey_box.addItems([
            "开始群发", "停止任务", "导入账号", "导出日志", "添加模板"
        ])
        self.hotkey_input = QLineEdit(self.cfg.get("hotkeys", {}).get("开始群发", "Ctrl+S"))
        hotkey_layout.addWidget(QLabel("快捷键设置:"))
        hotkey_layout.addWidget(self.hotkey_box)
        hotkey_layout.addWidget(self.hotkey_input)
        self.save_hotkey_btn = QPushButton("保存快捷键")
        self.save_hotkey_btn.clicked.connect(self.save_hotkey)
        hotkey_layout.addWidget(self.save_hotkey_btn)
        layout.addLayout(hotkey_layout)

        # 布局保存/应用
        btn_layout = QHBoxLayout()
        self.save_btn = QPushButton("保存布局设置")
        self.save_btn.clicked.connect(self.save_cfg)
        btn_layout.addWidget(self.save_btn)
        self.export_btn = QPushButton("导出布局")
        self.export_btn.clicked.connect(self.export_cfg)
        btn_layout.addWidget(self.export_btn)
        self.import_btn = QPushButton("导入布局")
        self.import_btn.clicked.connect(self.import_cfg)
        btn_layout.addWidget(self.import_btn)
        layout.addLayout(btn_layout)

        self.setLayout(layout)

    def save_cfg(self):
        cfg = {
            "show_log": self.show_log_chk.isChecked(),
            "show_sidebar": self.show_sidebar_chk.isChecked(),
            "width": int(self.width_input.text() or 1200),
            "height": int(self.height_input.text() or 700),
            "hotkeys": self.cfg.get("hotkeys", {}),
        }
        save_ui_cfg(cfg)
        QMessageBox.information(self, "已保存", "布局/参数已保存，下次重启生效。")
        self.cfg = cfg

    def save_hotkey(self):
        action = self.hotkey_box.currentText()
        key = self.hotkey_input.text().strip()
        if "hotkeys" not in self.cfg:
            self.cfg["hotkeys"] = {}
        self.cfg["hotkeys"][action] = key
        save_ui_cfg(self.cfg)
        QMessageBox.information(self, "快捷键已保存", f"{action} 已绑定到 {key}")

    def export_cfg(self):
        fname, _ = QFileDialog.getSaveFileName(self, "导出布局", "ui_settings.json", "JSON Files (*.json)")
        if not fname: return
        with open(fname, "w", encoding="utf-8") as f:
            json.dump(self.cfg, f, ensure_ascii=False, indent=2)
        QMessageBox.information(self, "导出成功", f"布局已导出到:\n{fname}")

    def import_cfg(self):
        fname, _ = QFileDialog.getOpenFileName(self, "导入布局", "", "JSON Files (*.json)")
        if not fname: return
        with open(fname, "r", encoding="utf-8") as f:
            cfg = json.load(f)
        save_ui_cfg(cfg)
        QMessageBox.information(self, "导入成功", "布局参数已导入，下次重启软件生效。")
        self.cfg = cfg
