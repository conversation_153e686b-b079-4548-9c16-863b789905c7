from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit, QHBoxLayout, QMessageBox
import requests, os, zipfile

UPDATE_URL = "https://yourdomain.com/em_sent_updates/"  # 你的主程序/插件包托管地址
PLUGIN_LIST_URL = UPDATE_URL + "plugin_list.json"
MAIN_UPDATE_URL = UPDATE_URL + "main.py"
PLUGINS_DIR = "plugins"

class PluginTab(QWidget):
    tab_name = "在线升级"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        self.info_label = QLabel("点击按钮检查并自动升级插件和主程序。")
        layout.addWidget(self.info_label)
        self.check_btn = QPushButton("检查更新")
        self.check_btn.clicked.connect(self.check_update)
        layout.addWidget(self.check_btn)
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)
        self.setLayout(layout)

    def check_update(self):
        try:
            self.log_box.append("正在获取插件列表...")
            resp = requests.get(PLUGIN_LIST_URL, timeout=10)
            plugin_list = resp.json()  # [{name: "xxx_plugin.py", url: "..."}, ...]
            for plugin in plugin_list:
                pname = plugin['name']
                url = plugin['url']
                local_path = os.path.join(PLUGINS_DIR, pname)
                update = True
                if os.path.exists(local_path):
                    with open(local_path, "rb") as f:
                        local_md5 = f.read()
                    r2 = requests.get(url)
                    if local_md5 == r2.content:
                        update = False
                if update:
                    r = requests.get(url)
                    with open(local_path, "wb") as f:
                        f.write(r.content)
                    self.log_box.append(f"已升级插件：{pname}")
            self.log_box.append("插件已检查/更新完毕。")

            # 主程序检测/更新
            r = requests.get(MAIN_UPDATE_URL)
            main_path = os.path.join(os.getcwd(), "main.py")
            with open(main_path, "wb") as f:
                f.write(r.content)
            self.log_box.append("主程序 main.py 已升级。")

            QMessageBox.information(self, "升级成功", "主程序和插件已更新完成。部分变更需重启软件生效。")
        except Exception as e:
            self.log_box.append(f"升级失败：{e}")
