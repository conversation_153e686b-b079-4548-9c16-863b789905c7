from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QGroupBox, QTextEdit, QLineEdit, QComboBox, QMessageBox
import threading
import random
import time

class PluginTab(QWidget):
    tab_name = "养号器"

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 账号池导入
        box1 = QGroupBox("导入待养号账号池")
        l1 = QHBoxLayout()
        self.account_file_input = QLineEdit()
        self.account_file_input.setPlaceholderText("账号池TXT路径（格式同注册导出）")
        l1.addWidget(QLabel("账号池:"))
        l1.addWidget(self.account_file_input)
        self.load_btn = QPushButton("加载账号池")
        self.load_btn.clicked.connect(self.load_accounts)
        l1.addWidget(self.load_btn)
        box1.setLayout(l1)
        layout.addWidget(box1)

        # 行为库设置
        box2 = QGroupBox("养号行为设置")
        l2 = QHBoxLayout()
        self.behavior_select = QComboBox()
        self.behavior_select.addItems([
            "随机行为库（推荐）",
            "基础养号（登录+收信+改名）",
            "移动端养号（IMAP/POP）",
            "极低频养号（超安全）",
            "自定义（后续可扩展）"
        ])
        l2.addWidget(QLabel("行为策略:"))
        l2.addWidget(self.behavior_select)
        box2.setLayout(l2)
        layout.addWidget(box2)

        # 参数设置
        box3 = QGroupBox("养号参数")
        l3 = QHBoxLayout()
        self.thread_input = QLineEdit()
        self.thread_input.setPlaceholderText("线程数，如10")
        l3.addWidget(QLabel("线程数:"))
        l3.addWidget(self.thread_input)
        self.days_input = QLineEdit()
        self.days_input.setPlaceholderText("养号天数,如3")
        l3.addWidget(QLabel("养号周期(天):"))
        l3.addWidget(self.days_input)
        box3.setLayout(l3)
        layout.addWidget(box3)

        # 开始/暂停按钮
        btn_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始养号")
        self.start_btn.clicked.connect(self.start_yn)
        btn_layout.addWidget(self.start_btn)
        self.stop_btn = QPushButton("暂停")
        self.stop_btn.setEnabled(False)
        btn_layout.addWidget(self.stop_btn)
        layout.addLayout(btn_layout)

        # 进度显示
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        layout.addWidget(self.log_box)

        # 导出健康账号按钮
        export_layout = QHBoxLayout()
        self.export_btn = QPushButton("导出健康账号")
        self.export_btn.clicked.connect(self.export_accounts)
        export_layout.addWidget(self.export_btn)
        layout.addLayout(export_layout)

        self.setLayout(layout)

        # 账号池数据
        self.raw_accounts = []
        self.healthy_accounts = []
        self.stop_flag = False

    def load_accounts(self):
        path = self.account_file_input.text().strip()
        try:
            with open(path, "r", encoding="utf-8") as f:
                self.raw_accounts = [line.strip() for line in f if line.strip()]
            self.log_box.append("账号池加载成功，数量: {}".format(len(self.raw_accounts)))
        except Exception as e:
            QMessageBox.warning(self, "账号池加载失败", str(e))

    def start_yn(self):
        if not self.raw_accounts:
            QMessageBox.warning(self, "缺少账号池", "请先加载账号池！")
            return
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.log_box.append("【开始养号】总数: {}".format(len(self.raw_accounts)))
        self.healthy_accounts.clear()
        self.stop_flag = False
        thread = threading.Thread(target=self.fake_yn)
        thread.start()

    def fake_yn(self):
        total = len(self.raw_accounts)
        for idx, acc in enumerate(self.raw_accounts):
            if self.stop_flag:
                self.log_box.append("【已暂停】剩余任务未执行")
                break
            # 模拟养号行为
            time.sleep(0.7)
            ok = random.random() > 0.05
            if ok:
                self.healthy_accounts.append(acc)
                self.log_box.append(f"账号 {idx+1}/{total} 养号成功: {acc.split('----')[0]}")
            else:
                self.log_box.append(f"账号 {idx+1}/{total} 养号失败（已隔离）: {acc.split('----')[0]}")
        self.log_box.append("【养号完成】健康账号: {} / 总数: {}".format(len(self.healthy_accounts), total))
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

    def export_accounts(self):
        if not self.healthy_accounts:
            QMessageBox.warning(self, "导出失败", "暂无健康账号！")
            return
        from PyQt5.QtWidgets import QFileDialog
        fname, _ = QFileDialog.getSaveFileName(self, "导出健康账号池", "healthy_accounts.txt", "Text Files (*.txt)")
        if fname:
            with open(fname, "w", encoding="utf-8") as f:
                f.write("\n".join(self.healthy_accounts))
            QMessageBox.information(self, "导出成功", "健康账号池已导出到:\n" + fname)
