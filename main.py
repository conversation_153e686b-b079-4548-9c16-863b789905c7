import os
import sys
import importlib.util
import zipfile
import logging
import traceback
from typing import List, Optional
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QMessageBox, 
    QSplashScreen, QLabel, QVBoxLayout, QWidget, QProgressBar
)
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal

def setup_paths_and_logging():
    """设置路径和日志配置"""
    global BASE_DIR, RESOURCE_DIR, PLUGINS_DIR, CONFIG_DIR, DATA_DIR, LOGS_DIR
    
    # 路径配置 - 统一处理
    if getattr(sys, 'frozen', False):
        BASE_DIR = os.path.dirname(sys.executable)
        RESOURCE_DIR = sys._MEIPASS if hasattr(sys, '_MEIPASS') else BASE_DIR
    else:
        BASE_DIR = os.path.dirname(os.path.abspath(__file__))
        RESOURCE_DIR = BASE_DIR

    # 设置工作目录为程序所在目录
    os.chdir(BASE_DIR)
    
    PLUGINS_DIR = os.path.join(BASE_DIR, "plugins")
    CONFIG_DIR = os.path.join(BASE_DIR, "config")
    DATA_DIR = os.path.join(BASE_DIR, "data")
    LOGS_DIR = os.path.join(BASE_DIR, "logs")

    # 创建必要目录
    required_dirs = [
        "logs", "data", "backup", "templates", "assets", 
        "plugins", "config", "utils"
    ]
    
    for folder in required_dirs:
        path = os.path.join(BASE_DIR, folder)
        try:
            os.makedirs(path, exist_ok=True)
        except Exception as e:
            print(f"警告: 无法创建目录 {path}: {e}")

    # 配置日志
    try:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(LOGS_DIR, 'app.log'), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    except Exception as e:
        print(f"日志配置失败: {e}")
        # 回退到基本日志配置
        logging.basicConfig(level=logging.INFO)

# 初始化路径和日志
setup_paths_and_logging()
logger = logging.getLogger(__name__)

class PluginLoader(QThread):
    """异步插件加载器"""
    plugin_loaded = pyqtSignal(object, str)
    loading_finished = pyqtSignal(list)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int, str)
    
    def run(self):
        try:
            plugins = self.load_plugins()
            self.loading_finished.emit(plugins)
        except Exception as e:
            error_msg = f"插件加载失败: {str(e)}\n{traceback.format_exc()}"
            self.error_occurred.emit(error_msg)
            logger.error(error_msg)
    
    def load_plugins(self) -> List:
        """动态加载插件"""
        plugins = []
        
        if not os.path.exists(PLUGINS_DIR):
            logger.warning(f"插件目录不存在，创建: {PLUGINS_DIR}")
            os.makedirs(PLUGINS_DIR, exist_ok=True)
            return plugins
        
        plugin_files = [f for f in os.listdir(PLUGINS_DIR) 
                       if (f.endswith(".py") and not f.startswith("__")) or f.endswith(".zip")]
        
        total_files = len(plugin_files)
        if total_files == 0:
            logger.info("没有找到插件文件")
            return plugins
        
        # 加载插件文件
        for index, filename in enumerate(plugin_files):
            try:
                self.progress_updated.emit(
                    int((index / total_files) * 100), 
                    f"正在加载: {filename}"
                )
                
                if filename.endswith(".py"):
                    self._load_python_plugin(filename, plugins)
                elif filename.endswith(".zip"):
                    self._load_zip_plugin(filename, plugins)
                    
            except Exception as e:
                logger.error(f"加载插件 {filename} 失败: {e}")
                continue
        
        self.progress_updated.emit(100, f"加载完成，共 {len(plugins)} 个插件")
        logger.info(f"成功加载 {len(plugins)} 个插件")
        return plugins
    
    def _load_python_plugin(self, filename: str, plugins: List):
        """加载Python插件"""
        module_name = filename[:-3]
        file_path = os.path.join(PLUGINS_DIR, filename)
        
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        if spec is None:
            raise ImportError(f"无法创建模块规范: {filename}")
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        if hasattr(module, "PluginTab"):
            plugins.append(module)
            self.plugin_loaded.emit(module, filename)
            logger.info(f"已加载插件: {filename}")
    
    def _load_zip_plugin(self, filename: str, plugins: List):
        """加载ZIP压缩插件"""
        zip_path = os.path.join(PLUGINS_DIR, filename)
        extract_dir = os.path.join(PLUGINS_DIR, f"zip_{filename[:-4]}")
        
        # 解压ZIP文件
        if not os.path.exists(extract_dir):
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
        
        # 加载解压后的Python文件
        for extracted_file in os.listdir(extract_dir):
            if extracted_file.endswith(".py"):
                module_name = extracted_file[:-3]
                file_path = os.path.join(extract_dir, extracted_file)
                
                spec = importlib.util.spec_from_file_location(module_name, file_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                if hasattr(module, "PluginTab"):
                    plugins.append(module)
                    logger.info(f"已从ZIP加载插件: {extracted_file}")

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.plugins = []
        self.plugin_tabs = {}
        self.splash = None
        self.init_ui()
        self.load_plugins_async()
    
    def init_ui(self):
        """初始化用户界面"""
        try:
            self.setWindowTitle("EM Sent 邮件群发工具 v2.0 - 作者: Awin - <EMAIL>")
            self.setMinimumSize(1000, 600)
            self.resize(1200, 720)
            
            # 设置图标
            icon_path = os.path.join(RESOURCE_DIR, "assets", "icon.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
            
            # 创建中央标签页窗口
            self.tabs = QTabWidget()
            self.tabs.setTabsClosable(False)
            self.tabs.setMovable(True)
            self.setCentralWidget(self.tabs)
            
            # 添加加载页面
            self.add_loading_tab()
            
            # 设置状态栏
            self.statusBar().showMessage("正在启动...")
            
            logger.info("主窗口初始化完成")
            
        except Exception as e:
            logger.error(f"UI初始化失败: {e}")
            raise
    
    def add_loading_tab(self):
        """添加加载页面"""
        loading_widget = QWidget()
        layout = QVBoxLayout()
        
        # 创建标题
        title_label = QLabel("""
        <h1>EM Sent 邮件群发工具</h1>
        <p><b>版本:</b> 2.0</p>
        <p><b>作者:</b> Awin</p>
        <p><b>邮箱:</b> <EMAIL></p>
        <hr>
        """)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        
        # 创建状态标签
        self.status_label = QLabel("正在初始化插件系统...")
        self.status_label.setAlignment(Qt.AlignCenter)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        
        layout.addWidget(title_label)
        layout.addWidget(self.status_label)
        layout.addWidget(self.progress_bar)
        layout.addStretch()
        
        loading_widget.setLayout(layout)
        self.tabs.addTab(loading_widget, "加载中...")
    
    def load_plugins_async(self):
        """异步加载插件"""
        try:
            self.plugin_loader = PluginLoader()
            self.plugin_loader.plugin_loaded.connect(self.on_plugin_loaded)
            self.plugin_loader.loading_finished.connect(self.on_loading_finished)
            self.plugin_loader.error_occurred.connect(self.on_loading_error)
            self.plugin_loader.progress_updated.connect(self.update_progress)
            self.plugin_loader.start()
        except Exception as e:
            logger.error(f"启动插件加载器失败: {e}")
            self.on_loading_error(str(e))
    
    def update_progress(self, value, message):
        """更新加载进度"""
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setValue(value)
        if hasattr(self, 'status_label'):
            self.status_label.setText(message)
        self.statusBar().showMessage(message)
    
    def on_plugin_loaded(self, plugin_module, filename):
        """单个插件加载完成"""
        pass  # 可以在这里显示加载进度
    
    def on_loading_finished(self, plugins):
        """所有插件加载完成"""
        try:
            self.plugins = plugins
            self.create_plugin_tabs()
            
            # 移除加载页面
            if self.tabs.count() > 0:
                self.tabs.removeTab(0)
            
            # 如果没有插件，显示提示
            if len(plugins) == 0:
                self.add_no_plugins_tab()
            
            self.statusBar().showMessage(f"就绪 - 已加载 {len(plugins)} 个插件")
            logger.info("插件加载完成，主窗口就绪")
            
        except Exception as e:
            logger.error(f"插件标签页创建失败: {e}")
            self.on_loading_error(f"插件标签页创建失败: {str(e)}")
    
    def on_loading_error(self, error_msg):
        """插件加载错误"""
        logger.error(f"插件加载错误: {error_msg}")
        
        # 移除加载页面
        if self.tabs.count() > 0:
            self.tabs.removeTab(0)
        
        # 添加错误信息页面
        self.add_error_tab(error_msg)
        self.statusBar().showMessage("插件加载失败")
    
    def add_no_plugins_tab(self):
        """添加无插件提示页面"""
        no_plugins_widget = QWidget()
        layout = QVBoxLayout()
        
        message_label = QLabel("""
        <h2>未找到插件</h2>
        <p>请将插件文件(.py或.zip)放入 plugins 文件夹中</p>
        <p>插件目录: {}</p>
        <p>重启程序后插件将自动加载</p>
        """.format(PLUGINS_DIR))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        
        layout.addWidget(message_label)
        no_plugins_widget.setLayout(layout)
        
        self.tabs.addTab(no_plugins_widget, "提示")
    
    def add_error_tab(self, error_msg: str):
        """添加错误信息页面"""
        error_widget = QWidget()
        layout = QVBoxLayout()
        
        error_label = QLabel(f"""
        <h2>加载错误</h2>
        <p>插件系统加载时出现错误:</p>
        <pre style="color: red; background: #f0f0f0; padding: 10px;">{error_msg}</pre>
        <p>请检查插件文件或联系开发者</p>
        """)
        error_label.setAlignment(Qt.AlignTop)
        error_label.setWordWrap(True)
        
        layout.addWidget(error_label)
        error_widget.setLayout(layout)
        
        self.tabs.addTab(error_widget, "错误")
    
    def create_plugin_tabs(self):
        """创建插件标签页"""
        # 按优先级排序插件
        priority_order = [
            "群发邮件", "批量群发", "账号管理", "收件人检测", 
            "内容模板库", "AI内容生成", "注册机", "养号器",
            "日志与报表", "统计分析", "SMTP监控", "任务计划"
        ]
        
        # 先添加优先级插件
        added_plugins = set()
        for priority_name in priority_order:
            for plugin in self.plugins:
                if hasattr(plugin, "PluginTab"):
                    try:
                        tab_instance = plugin.PluginTab()
                        tab_name = getattr(tab_instance, "tab_name", plugin.__name__)
                        
                        if tab_name == priority_name:
                            self.tabs.addTab(tab_instance, tab_name)
                            self.plugin_tabs[tab_name] = tab_instance
                            added_plugins.add(plugin)
                            break
                    except Exception as e:
                        logger.error(f"创建插件标签页失败 {plugin.__name__}: {e}")
        
        # 添加其余插件
        for plugin in self.plugins:
            if plugin not in added_plugins and hasattr(plugin, "PluginTab"):
                try:
                    tab_instance = plugin.PluginTab()
                    tab_name = getattr(tab_instance, "tab_name", plugin.__name__)
                    self.tabs.addTab(tab_instance, tab_name)
                    self.plugin_tabs[tab_name] = tab_instance
                except Exception as e:
                    logger.error(f"创建插件标签页失败 {plugin.__name__}: {e}")
    
    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            reply = QMessageBox.question(
                self, '确认退出', 
                '确定要退出EM Sent邮件群发工具吗？',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                logger.info("用户选择退出应用程序")
                event.accept()
            else:
                event.ignore()
        except Exception as e:
            logger.error(f"关闭事件处理失败: {e}")
            event.accept()  # 强制关闭

def check_environment():
    """检查运行环境"""
    issues = []
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        issues.append("Python版本过低，需要3.6或更高版本")
    
    # 检查PyQt5
    try:
        from PyQt5.QtWidgets import QApplication
    except ImportError:
        issues.append("PyQt5未安装或版本不兼容")
    
    # 检查关键目录权限
    try:
        test_file = os.path.join(BASE_DIR, "test_write_permission.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
    except Exception:
        issues.append("程序目录没有写入权限")
    
    return issues

def main():
    """主函数"""
    app = None
    try:
        # 检查环境
        env_issues = check_environment()
        if env_issues:
            print("环境检查失败:")
            for issue in env_issues:
                print(f"- {issue}")
            return 1
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        app.setApplicationName("EM Sent 邮件群发工具")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("Awin Studio")
        
        # 设置应用样式
        app.setStyle('Fusion')
        
        logger.info("应用程序启动")
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()  # 立即显示窗口
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {str(e)}\n{traceback.format_exc()}"
        logger.critical(error_msg)
        print(error_msg)  # 确保控制台也能看到错误
        
        if app:
            try:
                QMessageBox.critical(None, "启动错误", f"应用程序启动失败:\n{str(e)}")
            except:
                pass  # 如果连消息框都无法显示，就算了
        
        return 1

if __name__ == "__main__":
    sys.exit(main())